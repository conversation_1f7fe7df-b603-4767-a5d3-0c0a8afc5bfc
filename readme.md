打包 
GOOS=windows GOARCH=amd64 go build -o ./build/go.exe



创建一个go http服务 


# 初始化 init 
请求 https://api.scoa.fbatoll.com/tools/get_sorting.php
获得json  
{
    "3037": {
        "goodsnum": 2,
        "sorting_port": 4
        }
}
保存到本地文件为 csv
单号,总件数,分拣口
key,value.goodsnum,value.sorting_port


# 创建一个go http服务

访问接口会传递一个json 解析后 会有 item_number 

根据 item_number 实时去本地文件csv中查找 对应的 总件数,分拣口

返回json 

{
                        "status": 1,
                        "info": "success - ' . $goodsnum . '",
                        "data": {
                            "item_number": "' . $item_number . '",
                            "mode": 1,	
                            "sorting_port": ' . $sorting_port . '
                        }
                    }