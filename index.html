<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分拣服务器管理界面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
            display: flex;
            flex-direction: column;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            height: 50vh;
        }
        h1 {
            color: #1a1a1a;
            text-align: center;
            margin: 0 0 20px 0;
            grid-column: 1 / -1;
            font-size: 24px;
            font-weight: 600;
        }
        h2 {
            color: #1a1a1a;
            margin: 0 0 20px 0;
            font-size: 18px;
            font-weight: 500;
        }
        .section {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            padding: 20px;
            display: flex;
            flex-direction: column;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            justify-content: flex-start;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            background-color: #1890ff;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
        }
        button:hover {
            background-color: #096dd9;
            transform: translateY(-1px);
        }
        button.secondary {
            background-color: #52c41a;
        }
        button.secondary:hover {
            background-color: #389e0d;
        }
        button.active {
            background-color: #f5222d;
        }
        button.active:hover {
            background-color: #cf1322;
        }
        .query-section {
            flex: 1;
        }
        input {
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            width: 200px;
            transition: all 0.3s;
        }
        input:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
            outline: none;
        }
        #result, #logContent {
            margin-top: 15px;
            padding: 12px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: 'SF Mono', Monaco, Menlo, Consolas, monospace;
            font-size: 13px;
            line-height: 1.5;
            background-color: #fafafa;
            border: 1px solid #f0f0f0;
            flex: 1;
            overflow-y: auto;
        }
        .success {
            background-color: #f6ffed !important;
            border: 1px solid #b7eb8f !important;
        }
        .error {
            background-color: #fff2f0 !important;
            border: 1px solid #ffccc7 !important;
        }
        .log-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }
        .log-section {
            display: flex;
            flex: 1;
            min-height: 200px;
        }

        #logContent {
            height: 300px;
            overflow-y: auto;
            padding: 12px;
            border-radius: 6px;
            background-color: #fafafa;
            border: 1px solid #f0f0f0;
            font-family: 'SF Mono', Monaco, Menlo, Consolas, monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            scrollbar-width: thin;
            scrollbar-color: #d9d9d9 #f5f5f5;
        }
        
        #logContent::-webkit-scrollbar {
            width: 8px;
        }
        
        #logContent::-webkit-scrollbar-track {
            background: #f5f5f5;
            border-radius: 4px;
        }
        
        #logContent::-webkit-scrollbar-thumb {
            background-color: #d9d9d9;
            border-radius: 4px;
            border: 2px solid #f5f5f5;
        }
        
        #logContent::-webkit-scrollbar-thumb:hover {
            background-color: #bfbfbf;
        }
        
        @media (max-width: 1024px) {
            .container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>分拣服务器管理界面</h1>
        
        <div class="section">
            <div class="button-group">
                <button onclick="fetchData()">重新拉取数据</button>
            </div>

            <div class="query-section">
                <h2>单号查询</h2>
                <div class="button-group">
                    <input type="text" id="itemNumber" placeholder="请输入单号">
                    <button onclick="queryItem()">查询</button>
                </div>
                <div id="result"></div>
            </div>
        </div>
        <div class="section">
            <div class="button-group">
                <button onclick="toggleAutoScroll()" id="autoScrollBtn" class="secondary">开启实时滚动</button>
                <button onclick="clearLogs()" class="secondary">清空日志</button>
            </div>

            <div class="query-section">
                <h2>系统日志</h2>
                <div id="logContent"></div>
            </div>
        </div>

       
    </div>

    <script>
        let autoScroll = true;
        let logUpdateInterval = null;

        async function fetchData() {
            try {
                const response = await fetch('/fetch-data');
                const data = await response.json();
                const resultDiv = document.getElementById('result');
                resultDiv.className = data.success ? 'success' : 'error';
                resultDiv.textContent = data.message;
            } catch (error) {
                console.error('Error:', error);
                const resultDiv = document.getElementById('result');
                resultDiv.className = 'error';
                resultDiv.textContent = '操作失败：' + error.message;
            }
        }

        async function queryItem() {
            const itemNumber = document.getElementById('itemNumber').value.trim();
            if (!itemNumber) {
                alert('请输入单号');
                return;
            }

            try {
                const response = await fetch('/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ item_number: itemNumber })
                });
                const data = await response.json();
                const resultDiv = document.getElementById('result');
                resultDiv.className = data.status === 1 ? 'success' : 'error';
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                console.error('Error:', error);
                const resultDiv = document.getElementById('result');
                resultDiv.className = 'error';
                resultDiv.textContent = '查询失败：' + error.message;
            }
        }

        async function fetchLogs() {
            try {
                const response = await fetch('/logs');
                const logs = await response.text();
                const logContent = document.getElementById('logContent');
                logContent.textContent = logs;
                
                if (autoScroll) {
                    logContent.scrollTop = logContent.scrollHeight;
                }
            } catch (error) {
                console.error('Error fetching logs:', error);
            }
        }

        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            const autoScrollBtn = document.getElementById('autoScrollBtn');
            
            if (autoScroll) {
                autoScrollBtn.textContent = '关闭实时滚动';
                autoScrollBtn.classList.add('active');
                logUpdateInterval = setInterval(fetchLogs, 1000);
            } else {
                autoScrollBtn.textContent = '开启实时滚动';
                autoScrollBtn.classList.remove('active');
                if (logUpdateInterval) {
                    clearInterval(logUpdateInterval);
                }
            }
        }

        function clearLogs() {
            const logContent = document.getElementById('logContent');
            logContent.textContent = '';
        }

        // 页面加载时获取一次日志并开启自动滚动
        fetchLogs();
        toggleAutoScroll();
    </script>
</body>
</html> 