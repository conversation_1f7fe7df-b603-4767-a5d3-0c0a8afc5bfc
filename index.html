<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分拣服务器管理界面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            height: 50vh;
        }

        h1 {
            color: #1a1a1a;
            text-align: center;
            margin: 0 0 20px 0;
            grid-column: 1 / -1;
            font-size: 24px;
            font-weight: 600;
        }

        h2 {
            color: #1a1a1a;
            margin: 0 0 20px 0;
            font-size: 18px;
            font-weight: 500;
        }

        .section {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            justify-content: flex-start;
        }

        button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            background-color: #1890ff;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
        }

        button:hover {
            background-color: #096dd9;
            transform: translateY(-1px);
        }

        button.secondary {
            background-color: #52c41a;
        }

        button.secondary:hover {
            background-color: #389e0d;
        }

        button.active {
            background-color: #f5222d;
        }

        button.active:hover {
            background-color: #cf1322;
        }

        .query-section {
            flex: 1;
        }

        input {
            padding: 8px 12px;
            font-size: 14px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            width: 200px;
            transition: all 0.3s;
        }

        input:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }

        #result,
        #logContent {
            margin-top: 15px;
            padding: 12px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: 'SF Mono', Monaco, Menlo, Consolas, monospace;
            font-size: 13px;
            line-height: 1.5;
            background-color: #fafafa;
            border: 1px solid #f0f0f0;
            flex: 1;
            overflow-y: auto;
        }

        .success {
            background-color: #f6ffed !important;
            border: 1px solid #b7eb8f !important;
        }

        .error {
            background-color: #fff2f0 !important;
            border: 1px solid #ffccc7 !important;
        }

        .log-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }

        .log-section {
            display: flex;
            flex: 1;
            min-height: 200px;
        }

        #logContent {
            height: 100px;
            overflow-y: auto;
            padding: 12px;
            border-radius: 6px;
            background-color: #fafafa;
            border: 1px solid #f0f0f0;
            font-family: 'SF Mono', Monaco, Menlo, Consolas, monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            scrollbar-width: thin;
            scrollbar-color: #d9d9d9 #f5f5f5;
        }

        #logContent::-webkit-scrollbar {
            width: 8px;
        }

        #logContent::-webkit-scrollbar-track {
            background: #f5f5f5;
            border-radius: 4px;
        }

        #logContent::-webkit-scrollbar-thumb {
            background-color: #d9d9d9;
            border-radius: 4px;
            border: 2px solid #f5f5f5;
        }

        #logContent::-webkit-scrollbar-thumb:hover {
            background-color: #bfbfbf;
        }

        .config-section {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-top: 20px;
        }

        .config-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            padding: 8px;
            border: 1px solid #f0f0f0;
            border-radius: 6px;
            background-color: #fafafa;
        }

        .config-item input {
            width: 100px;
            padding: 4px 8px;
            font-size: 13px;
        }

        .config-item .range-input {
            width: 60px;
        }

        .config-item .port-input {
            width: 60px;
        }

        .config-item button {
            padding: 4px 8px;
            font-size: 12px;
            background-color: #ff4d4f;
            min-width: auto;
        }

        .config-item button:hover {
            background-color: #d9363e;
        }

        .add-config-form {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 15px;
            padding: 10px;
            border: 2px dashed #d9d9d9;
            border-radius: 6px;
            background-color: #fafafa;
        }

        .add-config-form input {
            width: 60px;
            padding: 4px 8px;
            font-size: 13px;
        }

        .config-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .basic-config {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            background-color: #f9f9f9;
        }

        .config-field {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .config-field label {
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .config-field select,
        .config-field input {
            padding: 6px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }

        .config-field .description {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        @media (max-width: 1024px) {
            .container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>分拣服务器管理界面</h1>

        <div class="section">
            <div class="button-group">
                <button onclick="fetchData()">重新拉取数据</button>
            </div>

            <div class="query-section">
                <h2>单号查询</h2>
                <div class="button-group">
                    <input type="text" id="itemNumber" placeholder="请输入单号">
                    <button onclick="queryItem()">查询</button>
                </div>
                <div id="result"></div>
            </div>
        </div>


        <div class="config-section">
            <h2>分拣配置管理</h2>

            <div class="basic-config">
                <div class="config-field">
                    <label for="sortingApi">分拣模式</label>
                    <select id="sortingApi">
                        <option value="0">本地配置模式</option>
                        <option value="1">API数据模式</option>
                    </select>
                    <div class="description">本地=直接读取请求数据，API=使用远程+CSV数据</div>
                </div>

                <div class="config-field">
                    <label for="sortingDefault">默认分拣口</label>
                    <input type="number" id="sortingDefault" min="1" placeholder="默认分拣口号">
                    <div class="description">当没有匹配的范围时使用的默认分拣口</div>
                </div>
            </div>

            <h3>分拣口范围配置</h3>
            <p style="color: #666; font-size: 14px; margin-bottom: 15px;">
                配置件数范围对应的分拣口号。格式：起始件数-结束件数 → 分拣口号。系统判断是由上至下！
            </p>

            <div id="configList">
                <!-- 配置项将通过 JavaScript 动态生成 -->
            </div>

            <div class="add-config-form">
                <span>添加新配置：</span>
                <input type="number" id="newRangeStart" placeholder="起始" min="1">
                <span>-</span>
                <input type="number" id="newRangeEnd" placeholder="结束" min="1">
                <span>→</span>
                <input type="number" id="newPort" placeholder="分拣口" min="1">
                <button onclick="addConfig()">添加</button>
            </div>

            <div class="config-actions">
                <button onclick="saveConfig()" class="secondary">保存配置</button>
                <button onclick="loadConfig()">重新加载</button>
            </div>

            <div id="configResult" style="margin-top: 15px;"></div>
        </div>

        <div class="section">
            <div class="button-group">
                <button onclick="toggleAutoScroll()" id="autoScrollBtn" class="secondary">开启实时滚动</button>
                <button onclick="clearLogs()" class="secondary">清空日志</button>
            </div>

            <div class="query-section">
                <h2>系统日志</h2>
                <div id="logContent"></div>
            </div>
        </div>

        <div style="margin: 20px 0;text-align: center;">Ver 20250805 @DaltonTam</div>

    </div>

    <script>
        let autoScroll = true;
        let logUpdateInterval = null;

        async function fetchData() {
            try {
                const response = await fetch('/fetch-data');
                const data = await response.json();
                const resultDiv = document.getElementById('result');
                resultDiv.className = data.success ? 'success' : 'error';
                resultDiv.textContent = data.message;
            } catch (error) {
                console.error('Error:', error);
                const resultDiv = document.getElementById('result');
                resultDiv.className = 'error';
                resultDiv.textContent = '操作失败：' + error.message;
            }
        }

        async function queryItem() {
            const itemNumber = document.getElementById('itemNumber').value.trim();
            if (!itemNumber) {
                alert('请输入单号');
                return;
            }

            try {
                const response = await fetch('/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ item_number: itemNumber })
                });
                const data = await response.json();
                const resultDiv = document.getElementById('result');
                resultDiv.className = data.status === 1 ? 'success' : 'error';
                resultDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                console.error('Error:', error);
                const resultDiv = document.getElementById('result');
                resultDiv.className = 'error';
                resultDiv.textContent = '查询失败：' + error.message;
            }
        }

        async function fetchLogs() {
            try {
                const response = await fetch('/logs');
                const logs = await response.text();
                const logContent = document.getElementById('logContent');
                logContent.textContent = logs;

                if (autoScroll) {
                    logContent.scrollTop = logContent.scrollHeight;
                }
            } catch (error) {
                console.error('Error fetching logs:', error);
            }
        }

        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            const autoScrollBtn = document.getElementById('autoScrollBtn');

            if (autoScroll) {
                autoScrollBtn.textContent = '关闭实时滚动';
                autoScrollBtn.classList.add('active');
                logUpdateInterval = setInterval(fetchLogs, 1000);
            } else {
                autoScrollBtn.textContent = '开启实时滚动';
                autoScrollBtn.classList.remove('active');
                if (logUpdateInterval) {
                    clearInterval(logUpdateInterval);
                }
            }
        }

        function clearLogs() {
            const logContent = document.getElementById('logContent');
            logContent.textContent = '';
        }

        // 配置管理相关变量
        let currentConfig = {
            sortingApi: 0,
            sortingDefault: 5,
            sortingLocal: {}
        };

        // 加载配置数据
        async function loadConfig() {
            try {
                const response = await fetch('/config');
                const config = await response.json();
                currentConfig = {
                    sortingApi: config.sortingApi || 0,
                    sortingDefault: config.sortingDefault || 5,
                    sortingLocal: config.sortingLocal || {}
                };
                renderConfigForm();
                renderConfigList();
                showConfigMessage('配置加载成功', 'success');
            } catch (error) {
                console.error('Error loading config:', error);
                showConfigMessage('配置加载失败：' + error.message, 'error');
            }
        }

        // 渲染基础配置表单
        function renderConfigForm() {
            document.getElementById('sortingApi').value = currentConfig.sortingApi;
            document.getElementById('sortingDefault').value = currentConfig.sortingDefault;
        }

        // 渲染配置列表
        function renderConfigList() {
            const configList = document.getElementById('configList');
            configList.innerHTML = '';

            // 按范围起始值排序
            const sortedEntries = Object.entries(currentConfig.sortingLocal).sort((a, b) => {
                const aStart = parseInt(a[0].split('-')[0]);
                const bStart = parseInt(b[0].split('-')[0]);
                return aStart - bStart;
            });

            sortedEntries.forEach(([range, port]) => {
                const configItem = document.createElement('div');
                configItem.className = 'config-item';

                const [start, end] = range.split('-');
                configItem.innerHTML = `
                    <span>件数范围：</span>
                    <input type="number" class="range-input" value="${start}" onchange="updateRange('${range}', this.value, '${end}', ${port})" min="1">
                    <span>-</span>
                    <input type="number" class="range-input" value="${end}" onchange="updateRange('${range}', '${start}', this.value, ${port})" min="1">
                    <span>→ 分拣口：</span>
                    <input type="number" class="port-input" value="${port}" onchange="updatePort('${range}', this.value)" min="1">
                    <button onclick="deleteConfig('${range}')">删除</button>
                `;

                configList.appendChild(configItem);
            });
        }

        // 更新范围
        function updateRange(oldRange, newStart, newEnd, port) {
            const start = parseInt(newStart);
            const end = parseInt(newEnd);

            if (start >= end) {
                alert('起始件数必须小于结束件数');
                renderConfigList(); // 重新渲染以恢复原值
                return;
            }

            const newRange = `${start}-${end}`;
            if (newRange !== oldRange) {
                if (currentConfig.sortingLocal[newRange] && newRange !== oldRange) {
                    alert('该范围已存在');
                    renderConfigList(); // 重新渲染以恢复原值
                    return;
                }

                delete currentConfig.sortingLocal[oldRange];
                currentConfig.sortingLocal[newRange] = port;
                renderConfigList();
            }
        }

        // 更新分拣口
        function updatePort(range, newPort) {
            const port = parseInt(newPort);
            if (port < 1) {
                alert('分拣口号必须大于0');
                renderConfigList(); // 重新渲染以恢复原值
                return;
            }
            currentConfig.sortingLocal[range] = port;
        }

        // 添加新配置
        function addConfig() {
            const startInput = document.getElementById('newRangeStart');
            const endInput = document.getElementById('newRangeEnd');
            const portInput = document.getElementById('newPort');

            const start = parseInt(startInput.value);
            const end = parseInt(endInput.value);
            const port = parseInt(portInput.value);

            if (!start || !end || !port) {
                alert('请填写完整的配置信息');
                return;
            }

            if (start >= end) {
                alert('起始件数必须小于结束件数');
                return;
            }

            if (port < 1) {
                alert('分拣口号必须大于0');
                return;
            }

            const range = `${start}-${end}`;
            if (currentConfig.sortingLocal[range]) {
                alert('该范围已存在');
                return;
            }

            currentConfig.sortingLocal[range] = port;
            renderConfigList();

            // 清空输入框
            startInput.value = '';
            endInput.value = '';
            portInput.value = '';
        }

        // 删除配置
        function deleteConfig(range) {
            if (confirm(`确定要删除范围 ${range} 的配置吗？`)) {
                delete currentConfig.sortingLocal[range];
                renderConfigList();
            }
        }

        // 更新基础配置
        function updateBasicConfig() {
            currentConfig.sortingApi = parseInt(document.getElementById('sortingApi').value);
            currentConfig.sortingDefault = parseInt(document.getElementById('sortingDefault').value);
        }

        // 保存配置
        async function saveConfig() {
            // 先更新基础配置
            updateBasicConfig();

            // 验证配置
            if (currentConfig.sortingDefault < 1) {
                alert('默认分拣口必须大于0');
                return;
            }

            try {
                const response = await fetch('/config/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(currentConfig)
                });

                const result = await response.json();
                if (result.success) {
                    showConfigMessage('配置保存成功', 'success');
                } else {
                    showConfigMessage('配置保存失败：' + result.message, 'error');
                }
            } catch (error) {
                console.error('Error saving config:', error);
                showConfigMessage('配置保存失败：' + error.message, 'error');
            }
        }

        // 显示配置操作结果消息
        function showConfigMessage(message, type) {
            const resultDiv = document.getElementById('configResult');
            resultDiv.className = type;
            resultDiv.textContent = message;

            // 3秒后清除消息
            setTimeout(() => {
                resultDiv.textContent = '';
                resultDiv.className = '';
            }, 3000);
        }

        // 添加基础配置字段的事件监听器
        document.getElementById('sortingApi').addEventListener('change', updateBasicConfig);
        document.getElementById('sortingDefault').addEventListener('change', updateBasicConfig);

        // 页面加载时获取一次日志并开启自动滚动，同时加载配置
        fetchLogs();
        toggleAutoScroll();
        loadConfig();
    </script>
</body>

</html>