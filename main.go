package main

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

// 配置结构体
type Config struct {
	API struct {
		DataURL string `json:"data_url"`
		APIPort int    `json:"api_port"`
		WebPort int    `json:"web_port"`
	} `json:"api"`
	SortingInfo    string         `json:"sortingInfo"`
	SortingAPI     int            `json:"sortingApi"`
	SortingDefault int            `json:"sortingDefault"`
	SortingLocal   map[string]int `json:"sortingLocal"`
}

// 全局配置变量
var config Config

// 读取配置文件
func loadConfig() error {
	file, err := os.Open("config.json")
	if err != nil {
		return fmt.Errorf("无法打开配置文件: %v", err)
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&config); err != nil {
		return fmt.Errorf("无法解析配置文件: %v", err)
	}

	return nil
}

// 根据件数获取分拣口
func getSortingPort(goodsNum int) int {
	// 如果使用 API 模式，返回 -1 表示使用 CSV 中的分拣口
	if config.SortingAPI == 1 {
		return -1
	}

	// 使用本地配置
	for rangeStr, port := range config.SortingLocal {
		parts := strings.Split(rangeStr, "-")
		if len(parts) != 2 {
			continue
		}

		minNum, err1 := strconv.Atoi(parts[0])
		maxNum, err2 := strconv.Atoi(parts[1])
		if err1 != nil || err2 != nil {
			continue
		}

		if goodsNum >= minNum && goodsNum <= maxNum {
			return port
		}
	}

	// 如果没有匹配的范围，返回默认分拣口
	return config.SortingDefault
}

// 确保 logs 文件夹存在
func ensureLogsDir() error {
	if _, err := os.Stat("logs"); os.IsNotExist(err) {
		return os.Mkdir("logs", 0755)
	}
	return nil
}

// 获取当前日期的日志文件名
func getLogFileName() string {
	return fmt.Sprintf("logs/%s.log", time.Now().Format("2006-01-02"))
}

// 写入日志
func writeLog(contextID, message string) error {
	logFile := getLogFileName()
	file, err := os.OpenFile(logFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer file.Close()

	logEntry := fmt.Sprintf("[%s] [ContextID: %s] %s\n", time.Now().Format("2006-01-02 15:04:05"), contextID, message)
	_, err = file.WriteString(logEntry)
	return err
}

// 从 API 获取数据并保存到 CSV
func fetchAndSaveData() (int, time.Time, error) {
	resp, err := http.Get(config.API.DataURL)
	if err != nil {
		return 0, time.Time{}, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, time.Time{}, err
	}

	var data map[string]struct {
		GoodsNum    int `json:"goodsnum"`
		SortingPort int `json:"sorting_port"`
	}
	if err := json.Unmarshal(body, &data); err != nil {
		return 0, time.Time{}, err
	}

	file, err := os.Create("data.csv")
	if err != nil {
		return 0, time.Time{}, err
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入 CSV 头
	if err := writer.Write([]string{"单号", "总件数", "分拣口"}); err != nil {
		return 0, time.Time{}, err
	}

	// 写入数据并统计条数
	count := 0
	for key, value := range data {
		if err := writer.Write([]string{key, strconv.Itoa(value.GoodsNum), strconv.Itoa(value.SortingPort)}); err != nil {
			return 0, time.Time{}, err
		}
		count++
	}

	// 获取文件修改时间
	fileInfo, err := os.Stat("data.csv")
	if err != nil {
		return 0, time.Time{}, err
	}
	modTime := fileInfo.ModTime()

	return count, modTime, nil
}

// 根据 item_number 查询 CSV 并返回 JSON
func handleRequest(w http.ResponseWriter, r *http.Request) {
	contextID := fmt.Sprintf("%d", time.Now().UnixNano()) // 生成上下文 ID

	// 每次请求前重新加载配置
	if err := loadConfig(); err != nil {
		http.Error(w, "配置文件加载失败", http.StatusInternalServerError)
		writeLog(contextID, fmt.Sprintf("配置文件加载失败: %v", err))
		return
	}

	// 读取请求体
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "无法读取请求体", http.StatusBadRequest)
		return
	}

	// 解析JSON请求体
	var requestBody struct {
		ItemNumber string `json:"item_number"`
	}
	if err := json.Unmarshal(body, &requestBody); err != nil {
		http.Error(w, "无效的JSON格式", http.StatusBadRequest)
		return
	}
	itemNumber := requestBody.ItemNumber

	// 记录请求开始
	writeLog(contextID, fmt.Sprintf("收到请求: item_number=%s", requestBody.ItemNumber))

	// 处理原始单号 比如 9959503-1 去掉最后一个 -1 有些是 123-1-1 去掉最后的-1 也有 -5 -5
	// 先检查是否包含-
	if !strings.Contains(itemNumber, "-") {
		itemNumber = itemNumber + "-1" // 如果没有-则默认添加-1
	}

	// 获取件数 因为本地需要判断
	packs := 1
	// 分割处理
	splitItemNumber := strings.Split(itemNumber, "-")
	// 去掉最后一个重新组成就是单号
	itemNumber = strings.Join(splitItemNumber[:len(splitItemNumber)-1], "-")
	// 获取件数
	if len(splitItemNumber) > 1 {
		packs, _ = strconv.Atoi(splitItemNumber[len(splitItemNumber)-1])
	}

	if itemNumber == "" {
		http.Error(w, "item_number is required", http.StatusBadRequest)
		writeLog(contextID, "请求失败: item_number 为空")
		return
	}

	file, err := os.Open("data.csv")
	if err != nil {
		http.Error(w, "Failed to open data file", http.StatusInternalServerError)
		writeLog(contextID, fmt.Sprintf("请求失败: 无法打开 data.csv, 错误=%v", err))
		return
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		http.Error(w, "Failed to read data file", http.StatusInternalServerError)
		writeLog(contextID, fmt.Sprintf("请求失败: 无法读取 data.csv, 错误=%v", err))
		return
	}
	var sortingPort int
	var goodsnum int
	// 根据配置决定使用哪种分拣口
	if config.SortingAPI == 1 {
		for _, record := range records[1:] { // 跳过表头
			// 只有用 APi 的才判断单号
			if record[0] == itemNumber {
				// 使用 CSV 中的分拣口
				goodsnum, _ = strconv.Atoi(record[1])
				sortingPort, _ = strconv.Atoi(record[2])
				response := map[string]interface{}{
					"status": 1,
					"info":   fmt.Sprintf("success - %d", goodsnum),
					"data": map[string]interface{}{
						"item_number":  itemNumber,
						"mode":         1,
						"sorting_port": sortingPort,
					},
				}

				w.Header().Set("Content-Type", "application/json")
				json.NewEncoder(w).Encode(response)
				writeLog(contextID, fmt.Sprintf("请求成功: item_number=%s, goodsnum=%d, sorting_port=%d", itemNumber, goodsnum, sortingPort))
				return
			}
		}

	} else {
		// 本地模式不判断单号
		goodsnum = packs
		// 使用本地配置的分拣口
		sortingPort = getSortingPort(goodsnum)
		// 返回成功响应
		response := map[string]interface{}{
			"status": 1,
			"info":   fmt.Sprintf("success - %d", goodsnum),
			"data": map[string]interface{}{
				"item_number":  itemNumber,
				"mode":         0,
				"sorting_port": sortingPort,
			},
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
		writeLog(contextID, fmt.Sprintf("请求成功: item_number=%s, goodsnum=%d, sorting_port=%d (配置模式: 0)", itemNumber, goodsnum, sortingPort))
		return
	}

	response := map[string]interface{}{
		"status": 0,
		"info":   fmt.Sprintf("error: 单号 %s 不存在", itemNumber),
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusNotFound)
	json.NewEncoder(w).Encode(response)
	writeLog(contextID, fmt.Sprintf("请求失败: item_number=%s 不存在", itemNumber))
}

// 处理静态文件请求
func handleStaticFiles(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path == "/" {
		http.ServeFile(w, r, "index.html")
		return
	}
	http.NotFound(w, r)
}

// 处理拉取数据请求
func handleFetchData(w http.ResponseWriter, r *http.Request) {
	// 拉取数据前重新加载配置
	if err := loadConfig(); err != nil {
		w.Header().Set("Content-Type", "application/json")
		response := map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("配置文件加载失败: %v", err),
		}
		json.NewEncoder(w).Encode(response)
		return
	}

	count, modTime, err := fetchAndSaveData()
	w.Header().Set("Content-Type", "application/json")

	response := make(map[string]interface{})
	if err != nil {
		response["success"] = false
		response["message"] = fmt.Sprintf("重新拉取数据失败: %v", err)
	} else {
		response["success"] = true
		response["message"] = fmt.Sprintf("数据重新拉取成功！\n获取到 %d 条数据\n数据截止时间: %s",
			count,
			modTime.Format("2006-01-02 15:04:05"))
	}

	json.NewEncoder(w).Encode(response)
}

// 获取最新的日志内容
func handleLogs(w http.ResponseWriter, r *http.Request) {
	logFile := getLogFileName()

	// 检查文件是否存在
	if _, err := os.Stat(logFile); os.IsNotExist(err) {
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, "今日暂无日志")
		return
	}

	// 读取文件
	content, err := os.ReadFile(logFile)
	if err != nil {
		http.Error(w, "无法读取日志文件", http.StatusInternalServerError)
		return
	}

	// 设置响应头
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	w.Write(content)
}

func main() {
	// 启动时加载配置
	if err := loadConfig(); err != nil {
		fmt.Printf("配置文件加载失败: %v\n", err)
		return
	}

	// 确保 logs 文件夹存在
	if err := ensureLogsDir(); err != nil {
		fmt.Println("无法创建 logs 文件夹:", err)
		return
	}

	// 设置路由
	http.HandleFunc("/", handleStaticFiles)
	http.HandleFunc("/query", handleRequest)
	http.HandleFunc("/fetch-data", handleFetchData)
	http.HandleFunc("/logs", handleLogs)

	// 启动Web服务
	go func() {
		fmt.Printf("API服务已启动在 :%d 端口\n", config.API.APIPort)
		http.ListenAndServe(fmt.Sprintf(":%d", config.API.APIPort), nil)
	}()

	// 启动Web界面服务
	fmt.Printf("Web界面已启动在 :%d 端口\n", config.API.WebPort)
	fmt.Printf("请在浏览器中访问: http://localhost:%d\n", config.API.WebPort)
	http.ListenAndServe(fmt.Sprintf(":%d", config.API.WebPort), http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 将请求转发到8855端口
		if r.URL.Path == "/" {
			http.ServeFile(w, r, "index.html")
			return
		}

		resp, err := http.DefaultClient.Do(&http.Request{
			Method: r.Method,
			URL: &url.URL{
				Scheme: "http",
				Host:   fmt.Sprintf("localhost:%d", config.API.APIPort),
				Path:   r.URL.Path,
			},
			Body:   r.Body,
			Header: r.Header,
		})

		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		defer resp.Body.Close()

		for k, v := range resp.Header {
			w.Header()[k] = v
		}
		w.WriteHeader(resp.StatusCode)
		io.Copy(w, resp.Body)
	}))
}
